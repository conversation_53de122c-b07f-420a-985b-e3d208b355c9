import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Grid,
  Card,
  CardContent,
  InputAdornment,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Search,
  Download,
  Upload,
  Inventory,
  Warning,
  CheckCircle,
  Error,
  QrCode,
} from '@mui/icons-material';

// Services
import { adaptiveStorageService } from '@/services/adaptive-storage';
import { adaptiveAuthService } from '@/services/adaptive-auth';
import { notificationService } from '@/services/notification';

// Components
import { CurrencyInput } from '@/components/CurrencyInput';
import { QuantityInput } from '@/components/QuantityInput';

// Types
import { Product, Category, StockStatus } from '@/types';

// Utils
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { formatSingleCurrency, getStockStatus, generateBarcode, generateQRCode, calculateUSDFromCDF } from '@/utils';
import { CSVUtils } from '@/utils/csv';
import { PRODUCT_COLUMNS } from '@/utils/csv-columns';
import { calculateProductProfit, validatePricing } from '@/utils/revenue';

const ProductsPage: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [stockFilter, setStockFilter] = useState<'all' | 'in_stock' | 'low_stock' | 'out_of_stock'>('all');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState({
    nom: '',
    description: '',
    prixAchatCDF: 0,
    prixCDF: 0,
    categorie: '',
    stock: 0,
    stockMin: 0,
    // Enhanced inventory management fields
    quantiteEnStock: 0,
    coutAchatStockCDF: 0,
    prixParPieceCDF: 0,
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [csvImportDialog, setCsvImportDialog] = useState(false);
  const [csvImportContent, setCsvImportContent] = useState('');
  const [settings, setSettings] = useState<any>({ tauxChangeUSDCDF: 2800 });

  const permissions = adaptiveAuthService.getUserPermissions();

  useEffect(() => {
    loadData();
  }, []);

  // Temporary function to clear corrupted data
  const handleClearData = () => {
    if (window.confirm('Cela va supprimer toutes les données et réinitialiser l\'application. Continuer?')) {
      localStorage.clear();
      window.location.reload();
    }
  };

  // Check for low stock when products change
  useEffect(() => {
    if (products.length > 0) {
      notificationService.checkLowStock(products);
    }
  }, [products]);

  useEffect(() => {
    filterProducts();
  }, [products, searchTerm, selectedCategory, stockFilter]);

  // Update rowsPerPage when filteredProducts changes and "Voir tout" is selected
  useEffect(() => {
    if (rowsPerPage === -1) {
      // Force re-render when showing all items and data changes
      setRowsPerPage(filteredProducts.length || 1);
    }
  }, [filteredProducts.length, rowsPerPage]);

  const loadData = async () => {
    try {
      const productsData = await adaptiveStorageService.getProducts();
      const settingsData = await adaptiveStorageService.getSettings();

      // Validate and clean product data
      const cleanProducts = productsData.map((product: any) => {
        const now = new Date().toISOString();
        return {
          ...product,
          dateCreation: product.dateCreation && !isNaN(new Date(product.dateCreation).getTime())
            ? product.dateCreation
            : now,
          dateModification: product.dateModification && !isNaN(new Date(product.dateModification).getTime())
            ? product.dateModification
            : now,
        };
      });

      setProducts(cleanProducts);
      setCategories(settingsData.categories);
      setSettings(settingsData);
    } catch (error) {
      console.error('Error loading products:', error);
      setError('Erreur lors du chargement des produits');
    }
  };

  const filterProducts = () => {
    let filtered = products;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.codeQR.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Category filter
    if (selectedCategory) {
      filtered = filtered.filter(product => product.categorie === selectedCategory);
    }

    // Stock filter
    if (stockFilter !== 'all') {
      filtered = filtered.filter(product => {
        const status = getStockStatus(product);
        return status === stockFilter;
      });
    }

    setFilteredProducts(filtered);
  };



  const getStockStatusColor = (status: StockStatus) => {
    switch (status) {
      case 'out_of_stock': return 'error';
      case 'low_stock': return 'warning';
      case 'in_stock': return 'success';
      default: return 'default';
    }
  };

  const getStockStatusIcon = (status: StockStatus) => {
    switch (status) {
      case 'out_of_stock': return <Error />;
      case 'low_stock': return <Warning />;
      case 'in_stock': return <CheckCircle />;
      default: return <Inventory />;
    }
  };

  const getStockStatusLabel = (status: StockStatus) => {
    switch (status) {
      case 'out_of_stock': return 'Rupture';
      case 'low_stock': return 'Stock bas';
      case 'in_stock': return 'En stock';
      default: return 'Inconnu';
    }
  };

  const handleOpenDialog = (product?: Product) => {
    if (product) {
      setEditingProduct(product);
      setFormData({
        nom: product.nom,
        description: product.description,
        prixAchatCDF: product.prixAchatCDF || 0,
        prixCDF: product.prixCDF,
        categorie: product.categorie,
        stock: product.stock,
        stockMin: product.stockMin,
        // Enhanced inventory management fields
        quantiteEnStock: product.quantiteEnStock || product.stock,
        coutAchatStockCDF: product.coutAchatStockCDF || (product.prixAchatCDF * product.stock),
        prixParPieceCDF: product.prixParPieceCDF || product.prixCDF,
      });
    } else {
      setEditingProduct(null);
      setFormData({
        nom: '',
        description: '',
        prixAchatCDF: 0,
        prixCDF: 0,
        categorie: '',
        stock: 0,
        stockMin: 0,
        // Enhanced inventory management fields
        quantiteEnStock: 0,
        coutAchatStockCDF: 0,
        prixParPieceCDF: 0,
      });
    }
    setOpenDialog(true);
    setError('');
    setSuccess('');

    // Fix for dialog input focus issues in Electron
    setTimeout(() => {
      const dialogInputs = document.querySelectorAll('div[role="dialog"] input, div[role="dialog"] textarea');
      dialogInputs.forEach((input) => {
        const element = input as HTMLElement;
        element.style.pointerEvents = 'auto';
        element.style.userSelect = 'text';
        if (!element.hasAttribute('tabindex') || element.tabIndex < 0) {
          element.tabIndex = 0;
        }
      });
    }, 100);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingProduct(null);
    setError('');
    setSuccess('');
  };

  const handleSaveProduct = async () => {
    // Validation
    if (!formData.nom.trim()) {
      setError('Le nom du produit est requis');
      return;
    }
    if (formData.prixAchatCDF <= 0) {
      setError('Le prix d\'achat doit être supérieur à zéro');
      return;
    }
    if (formData.prixCDF <= 0) {
      setError('Le prix de vente doit être supérieur à zéro');
      return;
    }
    if (formData.stock < 0) {
      setError('Le stock ne peut pas être négatif');
      return;
    }
    if (formData.stockMin < 0) {
      setError('Le stock minimum ne peut pas être négatif');
      return;
    }
    if (!formData.categorie) {
      setError('La catégorie est requise');
      return;
    }

    // Validate pricing logic
    const pricingValidation = validatePricing(formData.prixAchatCDF, formData.prixCDF);
    if (!pricingValidation.isValid) {
      setError(pricingValidation.errorMessage || 'Erreur de validation des prix');
      return;
    }

    const settings = await adaptiveStorageService.getSettings();
    const now = new Date().toISOString();

    // Calculate profit margins
    const profitCalculation = calculateProductProfit(
      formData.prixAchatCDF,
      formData.prixCDF,
      settings.tauxChangeUSDCDF
    );

    if (editingProduct) {
      // Update existing product
      const updatedProduct: Product = {
        ...editingProduct,
        nom: formData.nom.trim(),
        description: formData.description.trim(),
        prixAchatCDF: formData.prixAchatCDF,
        prixAchatUSD: calculateUSDFromCDF(formData.prixAchatCDF, settings.tauxChangeUSDCDF),
        prixCDF: formData.prixCDF,
        prixUSD: calculateUSDFromCDF(formData.prixCDF, settings.tauxChangeUSDCDF),
        beneficeUnitaireCDF: profitCalculation.beneficeUnitaireCDF,
        beneficeUnitaireUSD: profitCalculation.beneficeUnitaireUSD,
        categorie: formData.categorie,
        stock: formData.stock,
        stockMin: formData.stockMin,
        dateModification: now,
      };

      const updatedProducts = products.map(p =>
        p.id === editingProduct.id ? updatedProduct : p
      );

      setProducts(updatedProducts);
      await adaptiveStorageService.setProducts(updatedProducts);
      setSuccess('Produit mis à jour avec succès');
    } else {
      // Create new product
      const newProduct: Product = {
        id: Date.now().toString(),
        nom: formData.nom.trim(),
        description: formData.description.trim(),
        prixAchatCDF: formData.prixAchatCDF,
        prixAchatUSD: calculateUSDFromCDF(formData.prixAchatCDF, settings.tauxChangeUSDCDF),
        prixCDF: formData.prixCDF,
        prixUSD: calculateUSDFromCDF(formData.prixCDF, settings.tauxChangeUSDCDF),
        beneficeUnitaireCDF: profitCalculation.beneficeUnitaireCDF,
        beneficeUnitaireUSD: profitCalculation.beneficeUnitaireUSD,
        codeQR: generateQRCode(),
        categorie: formData.categorie,
        stock: formData.stock,
        stockMin: formData.stockMin,
        codeBarres: generateBarcode(),
        dateCreation: now,
        dateModification: now,
      };

      const updatedProducts = [...products, newProduct];
      setProducts(updatedProducts);
      await adaptiveStorageService.setProducts(updatedProducts);
      setSuccess('Produit créé avec succès');
    }

    setTimeout(() => {
      handleCloseDialog();
    }, 1500);
  };

  const handleDeleteProduct = async (product: Product) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le produit "${product.nom}" ?`)) {
      const updatedProducts = products.filter(p => p.id !== product.id);
      setProducts(updatedProducts);
      await adaptiveStorageService.setProducts(updatedProducts);
      setSuccess('Produit supprimé avec succès');
      setTimeout(() => setSuccess(''), 3000);
    }
  };

  const handleExportCSV = () => {
    const csvContent = CSVUtils.arrayToCSV(filteredProducts, PRODUCT_COLUMNS);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `SmartBoutique_Produits_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    setSuccess('Produits exportés en CSV avec succès');
    setTimeout(() => setSuccess(''), 3000);
  };

  const handleImportCSV = async () => {
    if (!csvImportContent.trim()) {
      setError('Veuillez saisir le contenu CSV à importer');
      return;
    }

    try {
      const importedProducts = CSVUtils.csvToArray(csvImportContent, PRODUCT_COLUMNS);
      const validation = CSVUtils.validateCSVData(importedProducts, PRODUCT_COLUMNS);

      if (!validation.isValid) {
        setError('Données CSV invalides: ' + validation.errors.join(', '));
        return;
      }

      // Generate missing IDs and dates
      const processedProducts = importedProducts.map((product, index) => ({
        ...product,
        id: product.id || Date.now().toString() + index,
        dateCreation: product.dateCreation || new Date().toISOString(),
        dateModification: product.dateModification || new Date().toISOString(),
        codeQR: product.codeQR || generateQRCode(),
        codeBarres: product.codeBarres || generateBarcode()
      }));

      // Merge with existing products (avoid duplicates by ID)
      const existingIds = new Set(products.map(p => p.id));
      const newProducts = processedProducts.filter(p => !existingIds.has(p.id));
      const updatedProducts = [...products, ...newProducts];

      setProducts(updatedProducts);
      await adaptiveStorageService.setProducts(updatedProducts);

      setSuccess(`${newProducts.length} produits importés avec succès`);
      setCsvImportDialog(false);
      setCsvImportContent('');
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Erreur lors de l\'importation: ' + (error as Error).message);
    }
  };



  const handleChangePage = (_: unknown, newPage: number) => {
    // Don't change page if showing all items
    if (rowsPerPage !== -1) {
      setPage(newPage);
    }
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value, 10);
    setRowsPerPage(value);
    setPage(0); // Reset to first page when changing rows per page
  };

  // Stats
  const totalProducts = products.length;
  const inStockProducts = products.filter(p => getStockStatus(p) === 'in_stock').length;
  const lowStockProducts = products.filter(p => getStockStatus(p) === 'low_stock').length;

  const totalInventoryValue = products.reduce((sum, p) => sum + (p.prixCDF * p.stock), 0);

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Inventaire
        </Typography>
        {permissions.canManageProducts && (
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => handleOpenDialog()}
          >
            Nouveau Produit
          </Button>
        )}
      </Box>

      {/* Success/Error Messages */}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Temporary Error Fix */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 2 }}
          action={
            <Button color="inherit" size="small" onClick={handleClearData}>
              Réinitialiser les données
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Total Produits
                  </Typography>
                  <Typography variant="h6">{totalProducts}</Typography>
                </Box>
                <Inventory color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    En Stock
                  </Typography>
                  <Typography variant="h6" color="success.main">{inStockProducts}</Typography>
                </Box>
                <CheckCircle color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Stock Bas
                  </Typography>
                  <Typography variant="h6" color="warning.main">{lowStockProducts}</Typography>
                </Box>
                <Warning color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Valeur Inventaire
                  </Typography>
                  <Typography variant="h6" color="primary">
                    {formatSingleCurrency(totalInventoryValue, 'CDF')}
                  </Typography>
                </Box>
                <Inventory color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Rechercher par nom, Code QR ou description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Catégorie</InputLabel>
              <Select
                value={selectedCategory}
                label="Catégorie"
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <MenuItem value="">Toutes les catégories</MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.nom}>
                    {category.nom}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Statut Stock</InputLabel>
              <Select
                value={stockFilter}
                label="Statut Stock"
                onChange={(e) => setStockFilter(e.target.value as any)}
              >
                <MenuItem value="all">Tous</MenuItem>
                <MenuItem value="in_stock">En stock</MenuItem>
                <MenuItem value="low_stock">Stock bas</MenuItem>
                <MenuItem value="out_of_stock">Rupture</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Box display="flex" gap={1}>
              <Tooltip title="Exporter CSV">
                <IconButton onClick={handleExportCSV}>
                  <Download />
                </IconButton>
              </Tooltip>
              <Tooltip title="Importer CSV">
                <IconButton onClick={() => setCsvImportDialog(true)}>
                  <Upload />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Products Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Produit</TableCell>
              <TableCell>Code QR</TableCell>
              <TableCell>Catégorie</TableCell>
              <TableCell align="right">Prix CDF</TableCell>
              <TableCell align="right">Prix USD</TableCell>
              <TableCell align="center">Stock</TableCell>
              <TableCell align="center">Statut</TableCell>
              <TableCell>Dernière Modif.</TableCell>
              {permissions.canManageProducts && <TableCell align="center">Actions</TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {(rowsPerPage === -1 ? filteredProducts : filteredProducts.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage))
              .map((product) => {
                const stockStatus = getStockStatus(product);
                return (
                  <TableRow
                    key={product.id}
                    hover
                    onClick={() => handleOpenDialog(product)}
                    sx={{ cursor: 'pointer' }}
                  >
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2">{product.nom}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {product.description}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        {product.codeQR}
                        <Tooltip title="Code QR">
                          <IconButton size="small">
                            <QrCode fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                    <TableCell>{product.categorie}</TableCell>
                    <TableCell align="right">
                      {formatSingleCurrency(product.prixCDF, 'CDF')}
                    </TableCell>
                    <TableCell align="right">
                      {product.prixUSD ? formatSingleCurrency(product.prixUSD, 'USD') : '-'}
                    </TableCell>
                    <TableCell align="center">
                      <Box>
                        <Typography variant="body2">
                          {product.stock}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Min: {product.stockMin}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        icon={getStockStatusIcon(stockStatus)}
                        label={getStockStatusLabel(stockStatus)}
                        color={getStockStatusColor(stockStatus)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {(() => {
                        try {
                          const date = new Date(product.dateModification);
                          if (isNaN(date.getTime())) {
                            return 'Date invalide';
                          }
                          return format(date, 'dd/MM/yyyy', { locale: fr });
                        } catch (error) {
                          return 'Date invalide';
                        }
                      })()}
                    </TableCell>
                    {permissions.canManageProducts && (
                      <TableCell align="center">
                        <Box display="flex" gap={1}>
                          <Tooltip title="Modifier">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenDialog(product)}
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          {adaptiveAuthService.hasRole(['super_admin']) && (
                            <Tooltip title="Supprimer">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteProduct(product)}
                              >
                                <Delete fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[
            5,
            10,
            25,
            50,
            100,
            { label: 'Voir tout', value: -1 }
          ]}
          component="div"
          count={filteredProducts.length}
          rowsPerPage={rowsPerPage === -1 ? filteredProducts.length : rowsPerPage}
          page={rowsPerPage === -1 ? 0 : page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Lignes par page:"
          labelDisplayedRows={({ from, to, count }) => {
            if (rowsPerPage === -1) {
              return `Affichage de tous les ${count} éléments`;
            }
            return `${from}-${to} sur ${count !== -1 ? count : `plus de ${to}`}`;
          }}
        />
      </TableContainer>

      {/* Product Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingProduct ? 'Modifier le Produit' : 'Nouveau Produit'}
        </DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Nom du produit *"
                value={formData.nom}
                onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <CurrencyInput
                label="Prix d'achat *"
                value={formData.prixAchatCDF}
                onChange={(value) => setFormData({ ...formData, prixAchatCDF: value })}
                min={0}
                max={10000000}
                step={100}
                exchangeRate={settings.tauxChangeUSDCDF}
                required
                showSlider={true}
                allowUSDInput={true}
                error={formData.prixAchatCDF <= 0}
                helperText={formData.prixAchatCDF <= 0 ? "Le prix d'achat doit être supérieur à zéro" : "Prix d'achat du produit en CDF"}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <CurrencyInput
                label="Prix de vente *"
                value={formData.prixCDF}
                onChange={(value) => setFormData({ ...formData, prixCDF: value })}
                min={0}
                max={10000000}
                step={100}
                exchangeRate={settings.tauxChangeUSDCDF}
                required
                showSlider={true}
                allowUSDInput={true}
                error={formData.prixCDF <= formData.prixAchatCDF}
                helperText={formData.prixCDF <= formData.prixAchatCDF ? "Le prix de vente doit être supérieur au prix d'achat pour générer un bénéfice" : "Prix de vente du produit en CDF"}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Catégorie *</InputLabel>
                <Select
                  value={formData.categorie}
                  label="Catégorie *"
                  onChange={(e) => setFormData({ ...formData, categorie: e.target.value })}
                >
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.nom}>
                      {category.nom}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <QuantityInput
                value={formData.stock}
                onChange={(value) => setFormData({ ...formData, stock: value })}
                min={0}
                max={999999}
                size="medium"
                showButtons={true}
                allowDirectInput={true}
                label="Stock actuel"
                helperText="Quantité actuelle en stock"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <QuantityInput
                value={formData.stockMin}
                onChange={(value) => setFormData({ ...formData, stockMin: value })}
                min={0}
                max={999999}
                size="medium"
                showButtons={true}
                allowDirectInput={true}
                label="Stock minimum"
                helperText="Seuil d'alerte pour stock bas"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSaveProduct} variant="contained">
            {editingProduct ? 'Mettre à jour' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* CSV Import Dialog */}
      <Dialog open={csvImportDialog} onClose={() => setCsvImportDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Importer des Produits depuis CSV</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Collez le contenu CSV des produits à importer. Format attendu: ID, Nom du Produit, Description, Prix CDF, Prix USD, Code QR, Catégorie, Stock, Stock Minimum, Code Barres, Date de Création, Date de Modification
          </DialogContentText>
          <TextField
            fullWidth
            multiline
            rows={10}
            value={csvImportContent}
            onChange={(e) => setCsvImportContent(e.target.value)}
            placeholder="ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification
1,iPhone 15,Smartphone Apple,2240000,800,SB123,Électronique,25,5,1234567890123,2024-01-01,2024-01-01"
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCsvImportDialog(false)}>Annuler</Button>
          <Button onClick={handleImportCSV} variant="contained">
            Importer
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProductsPage;
