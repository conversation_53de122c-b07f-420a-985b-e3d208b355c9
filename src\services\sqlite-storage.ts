/**
 * SQLite Storage Service for SmartBoutique Desktop
 * High-performance local database with ACID transactions
 */

import { Product, User, Sale, Debt, Expense, Settings, EmployeePayment } from '../types';

// Only import better-sqlite3 in Electron main process
let Database: any = null;
let app: any = null;
let join: any = null;

// Check if we're in Electron main process
const isElectronMain = typeof window === 'undefined' && typeof process !== 'undefined' && process.versions?.electron;

if (isElectronMain) {
  try {
    Database = require('better-sqlite3');
    app = require('electron').app;
    join = require('path').join;
  } catch (error) {
    console.warn('better-sqlite3 not available:', error);
  }
}

export class SQLiteStorageService {
  private db: any;
  private dbPath: string;
  private isAvailable: boolean = false;

  constructor() {
    if (!isElectronMain || !Database) {
      console.warn('SQLite not available in this context');
      return;
    }

    try {
      // Store database in user data directory
      const userDataPath = app?.getPath('userData') || './data';
      this.dbPath = join(userDataPath, 'smartboutique.db');
      this.initializeDatabase();
      this.isAvailable = true;
    } catch (error) {
      console.error('Failed to initialize SQLite:', error);
    }
  }

  private initializeDatabase(): void {
    if (!Database) {
      throw new Error('better-sqlite3 not available');
    }

    this.db = new Database(this.dbPath);

    // Enable WAL mode for better performance
    this.db.pragma('journal_mode = WAL');
    this.db.pragma('synchronous = NORMAL');
    this.db.pragma('cache_size = 1000');
    this.db.pragma('temp_store = memory');

    this.createTables();
    this.createIndexes();
  }

  private checkAvailability(): void {
    if (!this.isAvailable) {
      throw new Error('SQLite storage not available in this context');
    }
  }

  private createTables(): void {
    // Products table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        nom TEXT NOT NULL,
        description TEXT,
        prixAchatCDF REAL NOT NULL,
        prixAchatUSD REAL,
        prixCDF REAL NOT NULL,
        prixUSD REAL,
        beneficeUnitaireCDF REAL,
        beneficeUnitaireUSD REAL,
        codeQR TEXT,
        categorie TEXT,
        stock INTEGER,
        stockMin INTEGER,
        codeBarres TEXT,
        dateCreation TEXT,
        dateModification TEXT,
        quantiteEnStock INTEGER,
        coutAchatStockCDF REAL,
        coutAchatStockUSD REAL,
        prixParPieceCDF REAL,
        prixParPieceUSD REAL
      )
    `);

    // Users table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        nom TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        role TEXT NOT NULL,
        motDePasse TEXT NOT NULL,
        dateCreation TEXT,
        actif INTEGER DEFAULT 1
      )
    `);

    // Sales table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS sales (
        id TEXT PRIMARY KEY,
        date TEXT NOT NULL,
        client TEXT,
        produits TEXT NOT NULL,
        totalCDF REAL NOT NULL,
        totalUSD REAL,
        typePaiement TEXT NOT NULL,
        typeVente TEXT NOT NULL,
        vendeur TEXT NOT NULL,
        numeroRecu TEXT
      )
    `);

    // Debts table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS debts (
        id TEXT PRIMARY KEY,
        client TEXT NOT NULL,
        montantCDF REAL NOT NULL,
        montantUSD REAL,
        dateCreation TEXT NOT NULL,
        dateEcheance TEXT,
        statut TEXT NOT NULL,
        description TEXT,
        vendeur TEXT NOT NULL
      )
    `);

    // Expenses table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS expenses (
        id TEXT PRIMARY KEY,
        description TEXT NOT NULL,
        montantCDF REAL NOT NULL,
        montantUSD REAL,
        date TEXT NOT NULL,
        categorie TEXT NOT NULL,
        utilisateur TEXT NOT NULL,
        numeroRecu TEXT
      )
    `);

    // Settings table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    `);

    // Employee payments table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS employee_payments (
        id TEXT PRIMARY KEY,
        nomEmploye TEXT NOT NULL,
        montantCDF REAL NOT NULL,
        montantUSD REAL,
        datePaiement TEXT NOT NULL,
        methodePaiement TEXT NOT NULL,
        notes TEXT,
        creePar TEXT NOT NULL,
        dateCreation TEXT NOT NULL,
        dateModification TEXT
      )
    `);
  }

  private createIndexes(): void {
    // Performance indexes
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);
      CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);
      CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);
      CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);
      CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);
      CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);
      CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);
      CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);
      CREATE INDEX IF NOT EXISTS idx_employee_payments_date ON employee_payments(datePaiement);
      CREATE INDEX IF NOT EXISTS idx_employee_payments_employe ON employee_payments(nomEmploye);
      CREATE INDEX IF NOT EXISTS idx_employee_payments_methode ON employee_payments(methodePaiement);
      CREATE INDEX IF NOT EXISTS idx_employee_payments_cree_par ON employee_payments(creePar);
    `);
  }

  // Products CRUD operations
  getProducts(): Product[] {
    this.checkAvailability();
    const stmt = this.db.prepare('SELECT * FROM products ORDER BY nom');
    return stmt.all() as Product[];
  }

  getProduct(id: string): Product | undefined {
    this.checkAvailability();
    const stmt = this.db.prepare('SELECT * FROM products WHERE id = ?');
    return stmt.get(id) as Product | undefined;
  }

  setProducts(products: Product[]): void {
    const transaction = this.db.transaction((products: Product[]) => {
      // Clear existing products
      this.db.prepare('DELETE FROM products').run();
      
      // Insert new products
      const stmt = this.db.prepare(`
        INSERT INTO products (
          id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,
          beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,
          stockMin, codeBarres, dateCreation, dateModification
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      for (const product of products) {
        stmt.run(
          product.id, product.nom, product.description, product.prixAchatCDF,
          product.prixAchatUSD, product.prixCDF, product.prixUSD,
          product.beneficeUnitaireCDF, product.beneficeUnitaireUSD,
          product.codeQR, product.categorie, product.stock, product.stockMin,
          product.codeBarres, product.dateCreation, product.dateModification
        );
      }
    });

    transaction(products);
  }

  addProduct(product: Product): void {
    const stmt = this.db.prepare(`
      INSERT INTO products (
        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,
        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,
        stockMin, codeBarres, dateCreation, dateModification, quantiteEnStock,
        coutAchatStockCDF, coutAchatStockUSD, prixParPieceCDF, prixParPieceUSD
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      product.id, product.nom, product.description, product.prixAchatCDF,
      product.prixAchatUSD, product.prixCDF, product.prixUSD,
      product.beneficeUnitaireCDF, product.beneficeUnitaireUSD,
      product.codeQR, product.categorie, product.stock, product.stockMin,
      product.codeBarres, product.dateCreation, product.dateModification,
      product.quantiteEnStock, product.coutAchatStockCDF, product.coutAchatStockUSD,
      product.prixParPieceCDF, product.prixParPieceUSD
    );
  }

  updateProduct(product: Product): void {
    const stmt = this.db.prepare(`
      UPDATE products SET
        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,
        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,
        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,
        dateModification = ?
      WHERE id = ?
    `);

    stmt.run(
      product.nom, product.description, product.prixAchatCDF, product.prixAchatUSD,
      product.prixCDF, product.prixUSD, product.beneficeUnitaireCDF,
      product.beneficeUnitaireUSD, product.codeQR, product.categorie,
      product.stock, product.stockMin, product.codeBarres,
      product.dateModification, product.id
    );
  }

  deleteProduct(id: string): void {
    const stmt = this.db.prepare('DELETE FROM products WHERE id = ?');
    stmt.run(id);
  }

  // Users CRUD operations
  getUsers(): User[] {
    const stmt = this.db.prepare('SELECT * FROM users ORDER BY nom');
    const users = stmt.all() as any[];
    return users.map(user => ({
      ...user,
      actif: Boolean(user.actif)
    }));
  }

  setUsers(users: User[]): void {
    const transaction = this.db.transaction((users: User[]) => {
      this.db.prepare('DELETE FROM users').run();
      
      const stmt = this.db.prepare(`
        INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      for (const user of users) {
        stmt.run(
          user.id, user.nom, user.email, user.role,
          user.motDePasse, user.dateCreation, user.actif ? 1 : 0
        );
      }
    });

    transaction(users);
  }

  // Sales CRUD operations
  getSales(): Sale[] {
    const stmt = this.db.prepare('SELECT * FROM sales ORDER BY date DESC');
    const sales = stmt.all() as any[];
    return sales.map(sale => ({
      ...sale,
      produits: JSON.parse(sale.produits)
    }));
  }

  setSales(sales: Sale[]): void {
    const transaction = this.db.transaction((sales: Sale[]) => {
      this.db.prepare('DELETE FROM sales').run();
      
      const stmt = this.db.prepare(`
        INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      for (const sale of sales) {
        stmt.run(
          sale.id, sale.datevente, sale.nomClient, JSON.stringify(sale.produits),
          sale.totalCDF, sale.totalUSD, sale.methodePaiement, sale.typeVente,
          sale.vendeur, sale.numeroRecu
        );
      }
    });

    transaction(sales);
  }

  // Employee Payments CRUD operations
  getEmployeePayments(): EmployeePayment[] {
    this.checkAvailability();
    const stmt = this.db.prepare('SELECT * FROM employee_payments ORDER BY datePaiement DESC');
    return stmt.all() as EmployeePayment[];
  }

  getEmployeePayment(id: string): EmployeePayment | undefined {
    this.checkAvailability();
    const stmt = this.db.prepare('SELECT * FROM employee_payments WHERE id = ?');
    return stmt.get(id) as EmployeePayment | undefined;
  }

  addEmployeePayment(payment: EmployeePayment): void {
    this.checkAvailability();
    const stmt = this.db.prepare(`
      INSERT INTO employee_payments (
        id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,
        notes, creePar, dateCreation, dateModification
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      payment.id, payment.nomEmploye, payment.montantCDF, payment.montantUSD,
      payment.datePaiement, payment.methodePaiement, payment.notes,
      payment.creePar, payment.dateCreation, payment.dateModification
    );
  }

  updateEmployeePayment(payment: EmployeePayment): void {
    this.checkAvailability();
    const stmt = this.db.prepare(`
      UPDATE employee_payments SET
        nomEmploye = ?, montantCDF = ?, montantUSD = ?, datePaiement = ?,
        methodePaiement = ?, notes = ?, dateModification = ?
      WHERE id = ?
    `);

    stmt.run(
      payment.nomEmploye, payment.montantCDF, payment.montantUSD,
      payment.datePaiement, payment.methodePaiement, payment.notes,
      payment.dateModification, payment.id
    );
  }

  deleteEmployeePayment(id: string): void {
    this.checkAvailability();
    const stmt = this.db.prepare('DELETE FROM employee_payments WHERE id = ?');
    stmt.run(id);
  }

  setEmployeePayments(payments: EmployeePayment[]): void {
    this.checkAvailability();
    const transaction = this.db.transaction((payments: EmployeePayment[]) => {
      // Clear existing employee payments
      this.db.prepare('DELETE FROM employee_payments').run();

      // Insert new employee payments
      const stmt = this.db.prepare(`
        INSERT INTO employee_payments (
          id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,
          notes, creePar, dateCreation, dateModification
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      for (const payment of payments) {
        stmt.run(
          payment.id, payment.nomEmploye, payment.montantCDF, payment.montantUSD,
          payment.datePaiement, payment.methodePaiement, payment.notes,
          payment.creePar, payment.dateCreation, payment.dateModification
        );
      }
    });

    transaction(payments);
  }

  // Migration from CSV data
  migrateFromCSV(csvData: {
    products: Product[];
    users: User[];
    sales: Sale[];
    debts: Debt[];
    expenses: Expense[];
  }): void {
    console.log('Starting migration from CSV to SQLite...');

    const transaction = this.db.transaction(() => {
      if (csvData.products?.length) this.setProducts(csvData.products);
      if (csvData.users?.length) this.setUsers(csvData.users);
      if (csvData.sales?.length) this.setSales(csvData.sales);
      // Add debts and expenses migration...
    });

    transaction();
    console.log('Migration completed successfully');
  }

  // Export to CSV for backup
  exportToCSV(): {
    products: string;
    users: string;
    sales: string;
  } {
    // Implementation to export SQLite data back to CSV format
    // This maintains compatibility with existing CSV export functionality
    return {
      products: '', // CSV string
      users: '',    // CSV string
      sales: ''     // CSV string
    };
  }

  // Close database connection
  close(): void {
    this.db.close();
  }

  // Get database statistics
  getStats(): {
    products: number;
    users: number;
    sales: number;
    employeePayments: number;
    dbSize: number;
  } {
    const productCount = this.db.prepare('SELECT COUNT(*) as count FROM products').get() as { count: number };
    const userCount = this.db.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number };
    const saleCount = this.db.prepare('SELECT COUNT(*) as count FROM sales').get() as { count: number };
    const employeePaymentCount = this.db.prepare('SELECT COUNT(*) as count FROM employee_payments').get() as { count: number };

    return {
      products: productCount.count,
      users: userCount.count,
      sales: saleCount.count,
      employeePayments: employeePaymentCount.count,
      dbSize: 0 // Can be calculated from file size
    };
  }
}

export const sqliteStorageService = new SQLiteStorageService();
